package main

import (
	"log"
	"net/http"
	"os"
	"path/filepath"

	"github.com/gin-gonic/gin"
)

// APIResponse 统一响应格式
type APIResponse struct {
	Success   bool        `json:"success"`
	Data      interface{} `json:"data,omitempty"`
	Error     *APIError   `json:"error,omitempty"`
	Timestamp string      `json:"timestamp"`
	RequestID string      `json:"request_id"`
}

// APIError 错误信息
type APIError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// User 用户信息
type User struct {
	ID          string `json:"id"`
	Email       string `json:"email"`
	DisplayName string `json:"display_name"`
	AvatarURL   string `json:"avatar_url"`
}

// World 游戏世界
type World struct {
	ID             string `json:"id"`
	Name           string `json:"name"`
	Description    string `json:"description"`
	Theme          string `json:"theme"`
	IsPublic       bool   `json:"is_public"`
	MaxPlayers     int    `json:"max_players"`
	CurrentPlayers int    `json:"current_players"`
	CreatorID      string `json:"creator_id"`
	CreatedAt      string `json:"created_at"`
	UpdatedAt      string `json:"updated_at"`
}

func main() {
	// 设置 Gin 模式
	gin.SetMode(gin.ReleaseMode)

	// 创建路由器
	r := gin.Default()

	// 添加CORS中间件
	r.Use(corsMiddleware())
	
	// 设置静态文件服务
	setupStaticRoutes(r)
	
	// 设置 API 路由
	setupAPIRoutes(r)
	
	// 启动服务器
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}
	
	log.Printf("🚀 简化版AI文本游戏服务器启动在端口 %s", port)
	log.Printf("🌐 前端应用: http://localhost:%s", port)
	log.Printf("🔧 API文档: http://localhost:%s/api/v1", port)
	log.Printf("❤️  健康检查: http://localhost:%s/health", port)
	
	if err := r.Run(":" + port); err != nil {
		log.Fatal("启动服务器失败:", err)
	}
}

// setupStaticRoutes 设置静态文件路由
func setupStaticRoutes(r *gin.Engine) {
	// 静态文件目录
	staticDir := "web/static"
	distDir := filepath.Join(staticDir, "dist")
	
	// 检查构建产物是否存在
	if _, err := os.Stat(distDir); os.IsNotExist(err) {
		log.Printf("⚠️  前端构建产物不存在: %s", distDir)
		log.Printf("请先运行: cd web/frontend && npm run build")
	}
	
	// 服务静态资源
	r.Static("/assets", filepath.Join(distDir, "assets"))
	
	// 处理前端路由 - SPA 支持
	r.NoRoute(func(c *gin.Context) {
		path := c.Request.URL.Path
		
		// API 路由返回 404
		if len(path) >= 4 && path[:4] == "/api" {
			c.JSON(http.StatusNotFound, APIResponse{
				Success: false,
				Error: &APIError{
					Code:    "NOT_FOUND",
					Message: "API端点不存在",
				},
				Timestamp: "2024-01-01T00:00:00Z",
				RequestID: "test-request",
			})
			return
		}
		
		// 其他路由返回前端应用
		indexPath := filepath.Join(distDir, "index.html")
		if _, err := os.Stat(indexPath); err == nil {
			c.File(indexPath)
		} else {
			c.String(http.StatusNotFound, "前端应用未找到")
		}
	})
}

// setupAPIRoutes 设置 API 路由
func setupAPIRoutes(r *gin.Engine) {
	// 健康检查处理函数
	healthHandler := func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status": "ok",
			"service": "ai-text-game-simple",
			"version": "1.0.0",
			"auth_mode": "disabled", // 标识认证已禁用
		})
	}

	// 注册健康检查路由 - 支持两种路径
	r.GET("/health", healthHandler)      // 原有路径
	r.GET("/api/health", healthHandler)  // 新增API路径，兼容前端调用

	// API v1 路由组 - 开发环境无需认证
	v1 := r.Group("/api/v1")
	{
		// 用户相关 - 模拟认证，无需真实token
		v1.GET("/user/profile", getUserProfile)
		v1.POST("/auth/login", mockLogin)
		v1.POST("/auth/logout", mockLogout)

		// 世界相关 - 直接访问，无需认证
		v1.GET("/worlds", getWorlds)
		v1.POST("/worlds", createWorld)
		v1.GET("/worlds/:id", getWorld)

		// 游戏相关路由 - 兼容前端API调用
		gameGroup := v1.Group("/game")
		{
			// 世界管理
			gameGroup.GET("/my-worlds", getMyWorlds)
			gameGroup.GET("/public-worlds", getPublicWorlds)
			gameGroup.POST("/worlds", createWorld)
			gameGroup.GET("/worlds/:id", getWorld)
			gameGroup.PUT("/worlds/:id", updateWorld)
			gameGroup.DELETE("/worlds/:id", deleteWorld)

			// 角色管理
			gameGroup.GET("/my-characters", getMyCharacters)
			gameGroup.POST("/characters", createCharacter)
			gameGroup.GET("/characters/:id", getCharacter)
		}

		// 游戏状态和动作 - 直接访问，无需认证
		v1.GET("/games/:worldId/status", getGameStatus)
		v1.POST("/games/:worldId/actions", performAction)

		// AI相关路由 - 模拟AI生成功能
		aiGroup := v1.Group("/ai")
		{
			aiGroup.POST("/generate/scene", generateScene)
			aiGroup.GET("/interactions/history", getInteractionHistory)
			aiGroup.GET("/stats/token-usage", getTokenUsageStats)
		}

		// 添加更多开发测试接口
		v1.GET("/dev/test", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"message": "开发环境测试接口",
				"auth_required": false,
				"timestamp": "2024-01-01T00:00:00Z",
			})
		})
	}
}

// getUserProfile 获取用户信息
func getUserProfile(c *gin.Context) {
	user := User{
		ID:          "test-user-123",
		Email:       "<EMAIL>",
		DisplayName: "测试用户",
		AvatarURL:   "https://via.placeholder.com/64",
	}
	
	c.JSON(http.StatusOK, APIResponse{
		Success:   true,
		Data:      user,
		Timestamp: "2024-01-01T00:00:00Z",
		RequestID: "test-request",
	})
}

// mockLogin 模拟登录
func mockLogin(c *gin.Context) {
	var loginReq map[string]interface{}
	if err := c.ShouldBindJSON(&loginReq); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Error: &APIError{
				Code:    "INVALID_REQUEST",
				Message: "请求格式错误",
			},
			Timestamp: "2024-01-01T00:00:00Z",
			RequestID: "test-request",
		})
		return
	}
	
	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Data: gin.H{
			"token": "mock-jwt-token",
			"user": User{
				ID:          "test-user-123",
				Email:       "<EMAIL>",
				DisplayName: "测试用户",
				AvatarURL:   "https://via.placeholder.com/64",
			},
		},
		Timestamp: "2024-01-01T00:00:00Z",
		RequestID: "test-request",
	})
}

// mockLogout 模拟登出
func mockLogout(c *gin.Context) {
	c.JSON(http.StatusOK, APIResponse{
		Success:   true,
		Data:      gin.H{"message": "登出成功"},
		Timestamp: "2024-01-01T00:00:00Z",
		RequestID: "test-request",
	})
}

// getWorlds 获取世界列表
func getWorlds(c *gin.Context) {
	worlds := []World{
		{
			ID:          "world-1",
			Name:        "魔法森林",
			Description: "一个充满魔法和神秘生物的古老森林",
			IsPublic:    true,
			MaxPlayers:  10,
			CreatedAt:   "2024-01-01T00:00:00Z",
		},
		{
			ID:          "world-2",
			Name:        "赛博朋克都市",
			Description: "未来科技与黑暗现实交织的都市",
			IsPublic:    true,
			MaxPlayers:  20,
			CreatedAt:   "2024-01-01T00:00:00Z",
		},
		{
			ID:          "world-3",
			Name:        "中世纪王国",
			Description: "骑士、城堡和龙的传奇世界",
			IsPublic:    false,
			MaxPlayers:  15,
			CreatedAt:   "2024-01-01T00:00:00Z",
		},
	}
	
	c.JSON(http.StatusOK, APIResponse{
		Success:   true,
		Data:      worlds,
		Timestamp: "2024-01-01T00:00:00Z",
		RequestID: "test-request",
	})
}

// createWorld 创建新世界
func createWorld(c *gin.Context) {
	var worldReq map[string]interface{}
	if err := c.ShouldBindJSON(&worldReq); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Error: &APIError{
				Code:    "INVALID_REQUEST",
				Message: "请求格式错误",
			},
			Timestamp: "2024-01-01T00:00:00Z",
			RequestID: "test-request",
		})
		return
	}
	
	world := World{
		ID:          "world-new",
		Name:        worldReq["name"].(string),
		Description: worldReq["description"].(string),
		IsPublic:    worldReq["is_public"].(bool),
		MaxPlayers:  10,
		CreatedAt:   "2024-01-01T00:00:00Z",
	}
	
	c.JSON(http.StatusCreated, APIResponse{
		Success:   true,
		Data:      world,
		Timestamp: "2024-01-01T00:00:00Z",
		RequestID: "test-request",
	})
}

// getWorld 获取世界详情
func getWorld(c *gin.Context) {
	worldID := c.Param("id")
	
	world := World{
		ID:          worldID,
		Name:        "魔法森林",
		Description: "一个充满魔法和神秘生物的古老森林",
		IsPublic:    true,
		MaxPlayers:  10,
		CreatedAt:   "2024-01-01T00:00:00Z",
	}
	
	c.JSON(http.StatusOK, APIResponse{
		Success:   true,
		Data:      world,
		Timestamp: "2024-01-01T00:00:00Z",
		RequestID: "test-request",
	})
}

// getGameStatus 获取游戏状态
func getGameStatus(c *gin.Context) {
	worldID := c.Param("worldId")
	
	status := gin.H{
		"world_id": worldID,
		"status":   "active",
		"players":  3,
		"scene": gin.H{
			"id":          "scene-1",
			"name":        "森林入口",
			"description": "你站在一片古老森林的入口处，阳光透过茂密的树叶洒下斑驳的光影。",
		},
		"character": gin.H{
			"id":   "char-1",
			"name": "冒险者",
			"hp":   100,
			"mp":   50,
		},
	}
	
	c.JSON(http.StatusOK, APIResponse{
		Success:   true,
		Data:      status,
		Timestamp: "2024-01-01T00:00:00Z",
		RequestID: "test-request",
	})
}

// performAction 执行游戏动作
func performAction(c *gin.Context) {
	worldID := c.Param("worldId")
	
	var actionReq map[string]interface{}
	if err := c.ShouldBindJSON(&actionReq); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Error: &APIError{
				Code:    "INVALID_REQUEST",
				Message: "请求格式错误",
			},
			Timestamp: "2024-01-01T00:00:00Z",
			RequestID: "test-request",
		})
		return
	}
	
	result := gin.H{
		"world_id": worldID,
		"action":   actionReq["action"],
		"result":   "你的行动产生了有趣的结果...",
		"narrative": "随着你的行动，周围的环境发生了微妙的变化。",
		"effects": []gin.H{
			{
				"type":        "experience",
				"description": "获得了5点经验值",
				"value":       5,
			},
		},
	}
	
	c.JSON(http.StatusOK, APIResponse{
		Success:   true,
		Data:      result,
		Timestamp: "2024-01-01T00:00:00Z",
		RequestID: "test-request",
	})
}

// getMyWorlds 获取我的世界列表
func getMyWorlds(c *gin.Context) {
	worlds := []World{
		{
			ID:          "world-1",
			Name:        "魔法森林",
			Description: "一个充满魔法和神秘生物的古老森林",
			Theme:       "fantasy",
			IsPublic:    false,
			MaxPlayers:  4,
			CurrentPlayers: 1,
			CreatorID:   "test-user-123",
			CreatedAt:   "2024-01-01T00:00:00Z",
			UpdatedAt:   "2024-01-01T00:00:00Z",
		},
		{
			ID:          "world-2",
			Name:        "赛博朋克都市",
			Description: "2077年的未来都市，充满科技与霓虹",
			Theme:       "cyberpunk",
			IsPublic:    true,
			MaxPlayers:  8,
			CurrentPlayers: 3,
			CreatorID:   "test-user-123",
			CreatedAt:   "2024-01-01T00:00:00Z",
			UpdatedAt:   "2024-01-01T00:00:00Z",
		},
	}

	response := gin.H{
		"worlds": worlds,
		"total":  len(worlds),
		"page":   1,
		"limit":  10,
	}

	c.JSON(http.StatusOK, APIResponse{
		Success:   true,
		Data:      response,
		Timestamp: "2024-01-01T00:00:00Z",
		RequestID: "test-request",
	})
}

// getPublicWorlds 获取公开世界列表
func getPublicWorlds(c *gin.Context) {
	worlds := []World{
		{
			ID:          "world-3",
			Name:        "星际探险",
			Description: "探索未知的星系和外星文明",
			Theme:       "sci-fi",
			IsPublic:    true,
			MaxPlayers:  6,
			CurrentPlayers: 2,
			CreatorID:   "other-user-456",
			CreatedAt:   "2024-01-01T00:00:00Z",
			UpdatedAt:   "2024-01-01T00:00:00Z",
		},
		{
			ID:          "world-4",
			Name:        "中世纪王国",
			Description: "骑士、城堡和龙的传奇故事",
			Theme:       "medieval",
			IsPublic:    true,
			MaxPlayers:  10,
			CurrentPlayers: 5,
			CreatorID:   "other-user-789",
			CreatedAt:   "2024-01-01T00:00:00Z",
			UpdatedAt:   "2024-01-01T00:00:00Z",
		},
	}

	response := gin.H{
		"worlds": worlds,
		"total":  len(worlds),
		"page":   1,
		"limit":  20,
	}

	c.JSON(http.StatusOK, APIResponse{
		Success:   true,
		Data:      response,
		Timestamp: "2024-01-01T00:00:00Z",
		RequestID: "test-request",
	})
}

// updateWorld 更新世界
func updateWorld(c *gin.Context) {
	worldID := c.Param("id")

	// 模拟更新成功
	c.JSON(http.StatusOK, APIResponse{
		Success:   true,
		Data:      gin.H{"message": "世界更新成功", "world_id": worldID},
		Timestamp: "2024-01-01T00:00:00Z",
		RequestID: "test-request",
	})
}

// deleteWorld 删除世界
func deleteWorld(c *gin.Context) {
	worldID := c.Param("id")

	// 模拟删除成功
	c.JSON(http.StatusOK, APIResponse{
		Success:   true,
		Data:      gin.H{"message": "世界删除成功", "world_id": worldID},
		Timestamp: "2024-01-01T00:00:00Z",
		RequestID: "test-request",
	})
}

// getMyCharacters 获取我的角色列表
func getMyCharacters(c *gin.Context) {
	characters := []gin.H{
		{
			"id":          "char-1",
			"name":        "艾莉亚",
			"description": "一位勇敢的精灵法师",
			"world_id":    "world-1",
			"user_id":     "test-user-123",
			"created_at":  "2024-01-01T00:00:00Z",
			"updated_at":  "2024-01-01T00:00:00Z",
		},
		{
			"id":          "char-2",
			"name":        "赛博忍者",
			"description": "来自未来的神秘战士",
			"world_id":    "world-2",
			"user_id":     "test-user-123",
			"created_at":  "2024-01-01T00:00:00Z",
			"updated_at":  "2024-01-01T00:00:00Z",
		},
	}

	response := gin.H{
		"characters": characters,
		"total":      len(characters),
		"page":       1,
		"limit":      10,
	}

	c.JSON(http.StatusOK, APIResponse{
		Success:   true,
		Data:      response,
		Timestamp: "2024-01-01T00:00:00Z",
		RequestID: "test-request",
	})
}

// createCharacter 创建角色
func createCharacter(c *gin.Context) {
	// 模拟创建成功
	character := gin.H{
		"id":          "char-new",
		"name":        "新角色",
		"description": "刚刚创建的角色",
		"world_id":    "world-1",
		"user_id":     "test-user-123",
		"created_at":  "2024-01-01T00:00:00Z",
		"updated_at":  "2024-01-01T00:00:00Z",
	}

	c.JSON(http.StatusOK, APIResponse{
		Success:   true,
		Data:      character,
		Timestamp: "2024-01-01T00:00:00Z",
		RequestID: "test-request",
	})
}

// getCharacter 获取角色详情
func getCharacter(c *gin.Context) {
	characterID := c.Param("id")

	character := gin.H{
		"id":          characterID,
		"name":        "示例角色",
		"description": "这是一个示例角色",
		"world_id":    "world-1",
		"user_id":     "test-user-123",
		"created_at":  "2024-01-01T00:00:00Z",
		"updated_at":  "2024-01-01T00:00:00Z",
	}

	c.JSON(http.StatusOK, APIResponse{
		Success:   true,
		Data:      character,
		Timestamp: "2024-01-01T00:00:00Z",
		RequestID: "test-request",
	})
}

// generateScene 生成场景（模拟AI生成）
func generateScene(c *gin.Context) {
	var req map[string]interface{}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Error: &APIError{
				Code:    "INVALID_REQUEST",
				Message: "请求参数错误",
				Details: err.Error(),
			},
			Timestamp: "2024-01-01T00:00:00Z",
			RequestID: "test-request",
		})
		return
	}

	// 模拟AI生成的场景数据
	sceneData := gin.H{
		"content": "你站在一片神秘的森林中，古老的橡树高耸入云，阳光透过茂密的树叶洒下斑驳的光影。远处传来潺潺的流水声，空气中弥漫着青草和花香的味道。",
		"structured_data": gin.H{
			"name":        "神秘森林",
			"description": "一片充满魔法气息的古老森林",
			"type":        "forest",
			"atmosphere":  "神秘而宁静",
			"connections": gin.H{
				"north": "深林小径",
				"east":  "清澈溪流",
				"south": "森林入口",
			},
			"entities": []gin.H{
				{
					"name":        "古老橡树",
					"type":        "landmark",
					"description": "一棵有着数百年历史的巨大橡树",
				},
				{
					"name":        "森林小鹿",
					"type":        "creature",
					"description": "温顺的森林生物，眼神清澈",
				},
			},
		},
		"token_usage": 150,
	}

	c.JSON(http.StatusOK, APIResponse{
		Success:   true,
		Data:      sceneData,
		Timestamp: "2024-01-01T00:00:00Z",
		RequestID: "test-request",
	})
}

// getInteractionHistory 获取AI交互历史（模拟）
func getInteractionHistory(c *gin.Context) {
	interactions := []gin.H{
		{
			"id":               "interaction-1",
			"interaction_type": "scene_generation",
			"prompt":           "生成一个神秘的森林场景",
			"response":         "你站在一片神秘的森林中...",
			"token_usage":      150,
			"status":           "completed",
			"created_at":       "2024-01-01T00:00:00Z",
		},
		{
			"id":               "interaction-2",
			"interaction_type": "character_generation",
			"prompt":           "创建一个精灵法师角色",
			"response":         "艾莉亚是一位年轻的精灵法师...",
			"token_usage":      120,
			"status":           "completed",
			"created_at":       "2024-01-01T00:01:00Z",
		},
	}

	response := gin.H{
		"interactions": interactions,
		"total":        len(interactions),
		"page":         1,
		"limit":        10,
	}

	c.JSON(http.StatusOK, APIResponse{
		Success:   true,
		Data:      response,
		Timestamp: "2024-01-01T00:00:00Z",
		RequestID: "test-request",
	})
}

// getTokenUsageStats 获取Token使用统计（模拟）
func getTokenUsageStats(c *gin.Context) {
	stats := gin.H{
		"total_interactions":     25,
		"total_tokens":          3750,
		"avg_tokens_per_request": 150,
		"daily_usage": []gin.H{
			{
				"date":   "2024-01-01",
				"tokens": 450,
				"count":  3,
			},
			{
				"date":   "2024-01-02",
				"tokens": 600,
				"count":  4,
			},
		},
	}

	c.JSON(http.StatusOK, APIResponse{
		Success:   true,
		Data:      stats,
		Timestamp: "2024-01-01T00:00:00Z",
		RequestID: "test-request",
	})
}

// corsMiddleware CORS中间件，允许跨域请求
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 允许所有来源（开发环境）
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		c.Header("Access-Control-Expose-Headers", "Content-Length")
		c.Header("Access-Control-Allow-Credentials", "true")

		// 处理预检请求
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}
