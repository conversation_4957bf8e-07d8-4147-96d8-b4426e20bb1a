package ai

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"ai-text-game-iam-npc/internal/config"
	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/pkg/logger"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Service AI服务
type Service struct {
	config *config.Config
	db     *gorm.DB
	client *http.Client
	logger logger.Logger
}

// NewService 创建AI服务
func NewService(cfg *config.Config, db *gorm.DB, log logger.Logger) *Service {
	return &Service{
		config: cfg,
		db:     db,
		client: &http.Client{
			Timeout: time.Duration(cfg.AI.Timeout) * time.Second,
		},
		logger: log,
	}
}

// GenerateRequest AI生成请求
type GenerateRequest struct {
	Type           string                 `json:"type"`            // 生成类型：scene, character, event, dialogue等
	Prompt         string                 `json:"prompt"`          // 提示词
	Context        map[string]interface{} `json:"context"`         // 上下文信息
	Schema         map[string]interface{} `json:"schema"`          // 期望的响应结构
	MaxTokens      int                    `json:"max_tokens"`      // 最大token数
	Temperature    float64                `json:"temperature"`     // 温度参数
	WorldID        *uuid.UUID             `json:"world_id"`        // 世界ID
	UserID         *uuid.UUID             `json:"user_id"`         // 用户ID
}

// GenerateResponse AI生成响应
type GenerateResponse struct {
	Content      string                 `json:"content"`       // 生成的内容
	StructuredData map[string]interface{} `json:"structured_data"` // 结构化数据
	TokenUsage   int                    `json:"token_usage"`   // 使用的token数
	ResponseTime int                    `json:"response_time"` // 响应时间(毫秒)
}

// GenerateContent 生成内容
func (s *Service) GenerateContent(ctx context.Context, req *GenerateRequest) (*GenerateResponse, error) {
	startTime := time.Now()
	
	// 记录AI交互日志
	interaction := &models.AIInteraction{
		WorldID:         req.WorldID,
		UserID:          req.UserID,
		InteractionType: req.Type,
		Prompt:          req.Prompt,
		ResponseSchema:  models.JSON(req.Schema),
		Status:          "pending",
	}
	
	if err := s.db.Create(interaction).Error; err != nil {
		s.logger.Error("创建AI交互记录失败", "error", err)
	}
	
	var response *GenerateResponse
	var err error
	
	// 根据配置决定使用真实API还是Mock
	if s.config.AI.MockEnabled {
		response, err = s.generateMockContent(req)
	} else {
		response, err = s.generateRealContent(ctx, req)
	}
	
	responseTime := int(time.Since(startTime).Milliseconds())
	
	// 更新交互记录
	if err != nil {
		interaction.SetFailed(s.db, err.Error())
	} else {
		responseJSON, _ := json.Marshal(response.StructuredData)
		interaction.SetCompleted(s.db, string(responseJSON), response.TokenUsage, responseTime)
	}
	
	if response != nil {
		response.ResponseTime = responseTime
	}
	
	return response, err
}

// generateRealContent 调用真实的Windmill AI API生成内容
func (s *Service) generateRealContent(ctx context.Context, req *GenerateRequest) (*GenerateResponse, error) {
	s.logger.Info("开始调用Windmill AI API",
		"type", req.Type,
		"world_id", req.WorldID,
		"prompt_length", len(req.Prompt))

	// 构建符合Windmill API格式的请求
	windmillRequest := s.buildWindmillRequest(req)

	requestBody, err := json.Marshal(windmillRequest)
	if err != nil {
		s.logger.Error("序列化Windmill API请求失败", "error", err)
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	s.logger.Debug("Windmill API请求体", "request", string(requestBody))

	// 创建带超时的HTTP请求
	httpReq, err := http.NewRequestWithContext(ctx, "POST", s.config.AI.BaseURL+"/api/ai/generate", bytes.NewBuffer(requestBody))
	if err != nil {
		s.logger.Error("创建HTTP请求失败", "error", err)
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Accept", "application/json")
	if s.config.AI.Token != "" {
		httpReq.Header.Set("Authorization", "Bearer "+s.config.AI.Token)
	}

	// 发送请求，带重试机制
	var resp *http.Response
	var lastErr error

	for attempt := 0; attempt <= s.config.AI.MaxRetries; attempt++ {
		if attempt > 0 {
			s.logger.Info("重试Windmill API调用", "attempt", attempt, "max_retries", s.config.AI.MaxRetries)
			time.Sleep(s.config.AI.RetryDelay * time.Duration(attempt))
		}

		resp, lastErr = s.client.Do(httpReq)
		if lastErr == nil && resp.StatusCode < 500 {
			// 成功或客户端错误（不需要重试）
			break
		}

		if resp != nil {
			resp.Body.Close()
		}

		s.logger.Warn("Windmill API调用失败，准备重试",
			"attempt", attempt,
			"error", lastErr,
			"status_code", func() int {
				if resp != nil {
					return resp.StatusCode
				}
				return 0
			}())
	}

	if lastErr != nil {
		s.logger.Error("Windmill API调用最终失败", "error", lastErr, "attempts", s.config.AI.MaxRetries+1)
		return nil, fmt.Errorf("发送AI API请求失败: %w", lastErr)
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		s.logger.Error("读取Windmill API响应失败", "error", err)
		return nil, fmt.Errorf("读取AI API响应失败: %w", err)
	}

	s.logger.Debug("Windmill API响应",
		"status_code", resp.StatusCode,
		"response_length", len(responseBody))

	if resp.StatusCode != http.StatusOK {
		s.logger.Error("Windmill API返回错误状态",
			"status_code", resp.StatusCode,
			"response", string(responseBody))
		return nil, fmt.Errorf("AI API返回错误状态: %d, 响应: %s", resp.StatusCode, string(responseBody))
	}

	// 解析Windmill API响应
	response, err := s.parseWindmillResponse(responseBody, req.Type)
	if err != nil {
		s.logger.Error("解析Windmill API响应失败", "error", err, "response", string(responseBody))
		return nil, fmt.Errorf("解析AI API响应失败: %w", err)
	}

	s.logger.Info("Windmill AI API调用成功",
		"type", req.Type,
		"token_usage", response.TokenUsage,
		"content_length", len(response.Content))

	return response, nil
}

// buildWindmillRequest 构建符合Windmill API格式的请求
func (s *Service) buildWindmillRequest(req *GenerateRequest) map[string]interface{} {
	// 构建基础请求结构
	windmillReq := map[string]interface{}{
		"model": "gpt-4", // 默认使用GPT-4模型
		"messages": []map[string]interface{}{
			{
				"role":    "system",
				"content": s.buildSystemPrompt(req.Type),
			},
			{
				"role":    "user",
				"content": req.Prompt,
			},
		},
		"max_tokens":  req.MaxTokens,
		"temperature": req.Temperature,
		"response_format": map[string]interface{}{
			"type": "json_object",
		},
	}

	// 如果有上下文信息，添加到用户消息中
	if req.Context != nil && len(req.Context) > 0 {
		contextStr := s.formatContext(req.Context)
		if contextStr != "" {
			windmillReq["messages"] = append(windmillReq["messages"].([]map[string]interface{}), map[string]interface{}{
				"role":    "user",
				"content": "上下文信息: " + contextStr,
			})
		}
	}

	// 如果有Schema要求，添加到系统提示中
	if req.Schema != nil && len(req.Schema) > 0 {
		schemaPrompt := s.buildSchemaPrompt(req.Schema)
		systemMessage := windmillReq["messages"].([]map[string]interface{})[0]
		systemMessage["content"] = systemMessage["content"].(string) + "\n\n" + schemaPrompt
	}

	return windmillReq
}

// buildSystemPrompt 根据内容类型构建系统提示
func (s *Service) buildSystemPrompt(contentType string) string {
	basePrompt := "你是一个专业的AI文本游戏内容生成助手。请根据用户的要求生成高质量的游戏内容，并以JSON格式返回结果。"

	switch contentType {
	case "scene":
		return basePrompt + `

对于场景生成，请返回包含以下字段的JSON：
{
  "name": "场景名称",
  "description": "详细的场景描述，包含环境、氛围、关键特征等",
  "atmosphere": "场景氛围（如：神秘、温馨、紧张等）",
  "key_features": ["关键特征1", "关键特征2"],
  "possible_actions": ["可能的行动1", "可能的行动2"],
  "connections": [
    {
      "direction": "方向（如：北、南、东、西）",
      "description": "连接描述",
      "scene_name": "连接的场景名称"
    }
  ]
}`

	case "character":
		return basePrompt + `

对于角色生成，请返回包含以下字段的JSON：
{
  "name": "角色名称",
  "description": "角色的外观和基本描述",
  "personality": ["性格特征1", "性格特征2"],
  "background": "角色背景故事",
  "skills": ["技能1", "技能2"],
  "dialogue_style": "对话风格描述",
  "motivations": ["动机1", "动机2"]
}`

	case "event":
		return basePrompt + `

对于事件生成，请返回包含以下字段的JSON：
{
  "name": "事件名称",
  "description": "事件详细描述",
  "type": "事件类型（如：random、plot、character、environmental）",
  "priority": 事件优先级（1-10的数字）,
  "duration": 事件持续时间（分钟）,
  "effects": {
    "description": "事件效果描述",
    "consequences": ["后果1", "后果2"]
  }
}`

	default:
		return basePrompt + "\n\n请根据用户要求生成相应的游戏内容，并以结构化的JSON格式返回。"
	}
}

// formatContext 格式化上下文信息
func (s *Service) formatContext(context map[string]interface{}) string {
	if context == nil || len(context) == 0 {
		return ""
	}

	var parts []string
	for key, value := range context {
		if valueStr, ok := value.(string); ok && valueStr != "" {
			parts = append(parts, fmt.Sprintf("%s: %s", key, valueStr))
		}
	}

	return strings.Join(parts, "; ")
}

// buildSchemaPrompt 构建Schema提示
func (s *Service) buildSchemaPrompt(schema map[string]interface{}) string {
	if schema == nil || len(schema) == 0 {
		return ""
	}

	prompt := "请确保返回的JSON包含以下字段："
	for field, fieldType := range schema {
		prompt += fmt.Sprintf("\n- %s: %v", field, fieldType)
	}

	return prompt
}

// parseWindmillResponse 解析Windmill API响应
func (s *Service) parseWindmillResponse(responseBody []byte, contentType string) (*GenerateResponse, error) {
	// 首先尝试解析标准的OpenAI格式响应
	var openAIResponse struct {
		Choices []struct {
			Message struct {
				Content string `json:"content"`
			} `json:"message"`
		} `json:"choices"`
		Usage struct {
			TotalTokens int `json:"total_tokens"`
		} `json:"usage"`
	}

	if err := json.Unmarshal(responseBody, &openAIResponse); err == nil && len(openAIResponse.Choices) > 0 {
		content := openAIResponse.Choices[0].Message.Content

		// 尝试解析内容中的JSON
		var structuredData map[string]interface{}
		if err := json.Unmarshal([]byte(content), &structuredData); err != nil {
			s.logger.Warn("无法解析AI返回的JSON内容，使用原始文本", "error", err, "content", content)
			// 如果无法解析JSON，创建一个基本的结构化数据
			structuredData = s.createFallbackStructuredData(content, contentType)
		}

		return &GenerateResponse{
			Content:        content,
			StructuredData: structuredData,
			TokenUsage:     openAIResponse.Usage.TotalTokens,
		}, nil
	}

	// 如果不是标准OpenAI格式，尝试解析自定义格式
	var customResponse struct {
		Content        string                 `json:"content"`
		StructuredData map[string]interface{} `json:"structured_data"`
		TokenUsage     int                    `json:"token_usage"`
	}

	if err := json.Unmarshal(responseBody, &customResponse); err == nil {
		return &GenerateResponse{
			Content:        customResponse.Content,
			StructuredData: customResponse.StructuredData,
			TokenUsage:     customResponse.TokenUsage,
		}, nil
	}

	// 最后尝试直接解析为JSON对象
	var directJSON map[string]interface{}
	if err := json.Unmarshal(responseBody, &directJSON); err == nil {
		content := ""
		if desc, ok := directJSON["description"].(string); ok {
			content = desc
		} else if name, ok := directJSON["name"].(string); ok {
			content = name
		}

		return &GenerateResponse{
			Content:        content,
			StructuredData: directJSON,
			TokenUsage:     100, // 默认token使用量
		}, nil
	}

	return nil, fmt.Errorf("无法解析AI API响应格式")
}

// createFallbackStructuredData 创建备用的结构化数据
func (s *Service) createFallbackStructuredData(content, contentType string) map[string]interface{} {
	switch contentType {
	case "scene":
		return map[string]interface{}{
			"name":            "生成的场景",
			"description":     content,
			"atmosphere":      "未知",
			"key_features":    []string{},
			"possible_actions": []string{},
			"connections":     []interface{}{},
		}
	case "character":
		return map[string]interface{}{
			"name":           "生成的角色",
			"description":    content,
			"personality":    []string{},
			"background":     content,
			"skills":         []string{},
			"dialogue_style": "未知",
			"motivations":    []string{},
		}
	case "event":
		return map[string]interface{}{
			"name":        "生成的事件",
			"description": content,
			"type":        "random",
			"priority":    5,
			"duration":    30,
			"effects": map[string]interface{}{
				"description":   content,
				"consequences": []string{},
			},
		}
	default:
		return map[string]interface{}{
			"content":     content,
			"description": content,
		}
	}
}

// generateMockContent 生成Mock内容
func (s *Service) generateMockContent(req *GenerateRequest) (*GenerateResponse, error) {
	s.logger.Info("使用Mock模式生成AI内容", "type", req.Type)
	
	switch req.Type {
	case "scene":
		return s.generateMockScene(req)
	case "character":
		return s.generateMockCharacter(req)
	case "event":
		return s.generateMockEvent(req)
	case "dialogue":
		return s.generateMockDialogue(req)
	case "description":
		return s.generateMockDescription(req)
	default:
		return s.generateMockGeneral(req)
	}
}

// generateMockScene 生成Mock场景
func (s *Service) generateMockScene(req *GenerateRequest) (*GenerateResponse, error) {
	mockScenes := []map[string]interface{}{
		{
			"name":        "神秘森林",
			"description": "一片古老而神秘的森林，高大的树木遮天蔽日，林间弥漫着淡淡的雾气。偶尔能听到远处传来的鸟鸣声和树叶沙沙作响的声音。",
			"type":        "forest",
			"atmosphere":  "mysterious",
			"connections": map[string]string{
				"north": "山洞入口",
				"south": "村庄广场",
				"east":  "河边小径",
			},
			"entities": []string{"古老的橡树", "发光的蘑菇", "小溪"},
		},
		{
			"name":        "废弃城堡",
			"description": "一座年代久远的城堡，石墙斑驳，藤蔓缠绕。城堡内部阴暗潮湿，回音在空旷的大厅中回荡。",
			"type":        "castle",
			"atmosphere":  "eerie",
			"connections": map[string]string{
				"west":  "城堡花园",
				"north": "塔楼",
				"down":  "地下室",
			},
			"entities": []string{"破损的盔甲", "古老的画像", "蜘蛛网"},
		},
	}
	
	// 随机选择一个场景
	selectedScene := mockScenes[time.Now().Unix()%int64(len(mockScenes))]
	
	return &GenerateResponse{
		Content:        selectedScene["description"].(string),
		StructuredData: selectedScene,
		TokenUsage:     150,
	}, nil
}

// generateMockCharacter 生成Mock角色
func (s *Service) generateMockCharacter(req *GenerateRequest) (*GenerateResponse, error) {
	character := map[string]interface{}{
		"name":        "艾莉娅",
		"description": "一位年轻的精灵法师，有着银色的长发和翠绿的眼睛。",
		"type":        "npc",
		"personality": []string{"智慧", "善良", "好奇"},
		"skills":      []string{"魔法", "草药学"},
		"background":  "来自精灵王国的年轻法师。",
	}

	return &GenerateResponse{
		Content:        character["description"].(string),
		StructuredData: character,
		TokenUsage:     120,
	}, nil
}


// generateMockEvent 生成Mock事件
func (s *Service) generateMockEvent(req *GenerateRequest) (*GenerateResponse, error) {
	mockEvents := []map[string]interface{}{
		{
			"name":        "神秘商人的到来",
			"description": "一位穿着华丽长袍的神秘商人来到了村庄，他的马车上装满了奇异的物品和魔法道具。",
			"type":        "encounter",
			"priority":    5,
			"duration":    30,
			"effects": map[string]interface{}{
				"new_items":     []string{"魔法药水", "古老地图", "神秘护符"},
				"new_quests":    []string{"寻找失落的宝藏"},
				"reputation":    10,
			},
		},
		{
			"name":        "暴风雨来袭",
			"description": "天空突然乌云密布，雷声轰鸣，一场猛烈的暴风雨即将来临。所有户外活动都必须暂停。",
			"type":        "weather",
			"priority":    3,
			"duration":    60,
			"effects": map[string]interface{}{
				"weather_change": "storm",
				"visibility":     "low",
				"movement_speed": 0.5,
			},
		},
	}
	
	selectedEvent := mockEvents[time.Now().Unix()%int64(len(mockEvents))]
	
	return &GenerateResponse{
		Content:        selectedEvent["description"].(string),
		StructuredData: selectedEvent,
		TokenUsage:     100,
	}, nil
}

// generateMockDialogue 生成Mock对话
func (s *Service) generateMockDialogue(req *GenerateRequest) (*GenerateResponse, error) {
	mockDialogues := []string{
		"欢迎来到我们的村庄，陌生人。你看起来像是从很远的地方来的。",
		"这里最近发生了一些奇怪的事情，也许你能帮助我们解决这个问题。",
		"小心那片森林，据说里面住着危险的生物。",
		"你有什么需要的吗？我这里有各种各样的物品。",
		"传说中的宝藏就在那座古老的城堡里，但是没有人敢去寻找。",
	}
	
	selectedDialogue := mockDialogues[time.Now().Unix()%int64(len(mockDialogues))]
	
	return &GenerateResponse{
		Content: selectedDialogue,
		StructuredData: map[string]interface{}{
			"dialogue": selectedDialogue,
			"emotion":  "neutral",
			"intent":   "information",
		},
		TokenUsage: 50,
	}, nil
}

// generateMockDescription 生成Mock描述
func (s *Service) generateMockDescription(req *GenerateRequest) (*GenerateResponse, error) {
	descriptions := []string{
		"这是一个充满魔法和奇迹的世界，古老的传说在这里成为现实。",
		"微风轻抚过草地，带来了远方花朵的香气。",
		"夕阳西下，金色的光芒洒在大地上，一切都显得那么宁静美好。",
		"古老的石碑上刻着神秘的符文，似乎在诉说着久远的故事。",
		"篝火在夜晚中跳跃着，温暖的光芒驱散了黑暗和寒冷。",
	}
	
	selectedDescription := descriptions[time.Now().Unix()%int64(len(descriptions))]
	
	return &GenerateResponse{
		Content: selectedDescription,
		StructuredData: map[string]interface{}{
			"description": selectedDescription,
			"mood":        "peaceful",
			"style":       "descriptive",
		},
		TokenUsage: 80,
	}, nil
}

// generateMockGeneral 生成通用Mock内容
func (s *Service) generateMockGeneral(req *GenerateRequest) (*GenerateResponse, error) {
	return &GenerateResponse{
		Content: fmt.Sprintf("这是一个关于%s的Mock响应。在实际环境中，这里会调用真实的AI API来生成内容。", req.Type),
		StructuredData: map[string]interface{}{
			"type":    req.Type,
			"mock":    true,
			"prompt":  req.Prompt,
			"context": req.Context,
		},
		TokenUsage: 75,
	}, nil
}

// GetInteractionHistory 获取AI交互历史
func (s *Service) GetInteractionHistory(worldID *uuid.UUID, userID *uuid.UUID, limit int) ([]models.AIInteraction, error) {
	var interactions []models.AIInteraction
	query := s.db.Order("created_at DESC")
	
	if worldID != nil {
		query = query.Where("world_id = ?", *worldID)
	}
	
	if userID != nil {
		query = query.Where("user_id = ?", *userID)
	}
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	err := query.Find(&interactions).Error
	return interactions, err
}

// GetTokenUsageStats 获取token使用统计
func (s *Service) GetTokenUsageStats(worldID *uuid.UUID, userID *uuid.UUID, days int) (map[string]interface{}, error) {
	var stats struct {
		TotalInteractions int64 `json:"total_interactions"`
		TotalTokens       int   `json:"total_tokens"`
		AvgTokensPerReq   int   `json:"avg_tokens_per_request"`
	}

	query := s.db.Model(&models.AIInteraction{}).Where("status = ?", "completed")

	if worldID != nil {
		query = query.Where("world_id = ?", *worldID)
	}

	if userID != nil {
		query = query.Where("user_id = ?", *userID)
	}

	if days > 0 {
		query = query.Where("created_at >= ?", time.Now().AddDate(0, 0, -days))
	}

	// 获取总交互数
	query.Count(&stats.TotalInteractions)
	
	// 获取总token数
	var totalTokens sql.NullInt64
	query.Select("SUM(token_usage)").Scan(&totalTokens)
	stats.TotalTokens = int(totalTokens.Int64)
	
	// 计算平均值
	if stats.TotalInteractions > 0 {
		stats.AvgTokensPerReq = stats.TotalTokens / int(stats.TotalInteractions)
	}
	
	return map[string]interface{}{
		"total_interactions":     stats.TotalInteractions,
		"total_tokens":          stats.TotalTokens,
		"avg_tokens_per_request": stats.AvgTokensPerReq,
	}, nil
}
