package game

import (
	"context"
	"fmt"

	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/pkg/logger"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// WorldService 世界管理服务
type WorldService struct {
	db     *gorm.DB
	logger logger.Logger
}

// NewWorldService 创建世界管理服务
func NewWorldService(db *gorm.DB, logger logger.Logger) *WorldService {
	return &WorldService{
		db:     db,
		logger: logger,
	}
}

// CreateWorld 创建新世界
func (s *WorldService) CreateWorld(ctx context.Context, creatorID uuid.UUID, name, description string, config map[string]interface{}) (*models.World, error) {
	s.logger.Info("开始创建世界",
		"creator_id", creatorID,
		"name", name,
		"description_length", len(description),
		"config_keys", getMapKeys(config))

	// 验证输入参数
	if name == "" {
		s.logger.Error("世界名称不能为空")
		return nil, fmt.Errorf("世界名称不能为空")
	}

	if creatorID == uuid.Nil {
		s.logger.Error("创建者ID无效")
		return nil, fmt.Errorf("创建者ID无效")
	}

	// 创建世界对象
	world := &models.World{
		Name:        name,
		Description: &description,
		CreatorID:   creatorID,
		WorldConfig: models.JSON(config),
		Status:      "active",
		IsPublic:    false,
		MaxPlayers:  10,
	}

	s.logger.Info("准备保存世界到数据库",
		"world_name", world.Name,
		"world_status", world.Status,
		"max_players", world.MaxPlayers)

	// 开始数据库事务
	tx := s.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		s.logger.Error("开始数据库事务失败", "error", tx.Error)
		return nil, fmt.Errorf("开始数据库事务失败: %w", tx.Error)
	}

	// 创建世界记录
	if err := tx.Create(world).Error; err != nil {
		tx.Rollback()
		s.logger.Error("创建世界记录失败",
			"error", err,
			"creator_id", creatorID,
			"name", name,
			"sql_error", err.Error())
		return nil, fmt.Errorf("创建世界失败: %w", err)
	}

	s.logger.Info("世界记录创建成功",
		"world_id", world.ID,
		"generated_id", world.ID.String())

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		s.logger.Error("提交数据库事务失败", "error", err, "world_id", world.ID)
		return nil, fmt.Errorf("提交事务失败: %w", err)
	}

	s.logger.Info("世界创建完成",
		"world_id", world.ID,
		"creator_id", creatorID,
		"name", name,
		"created_at", world.CreatedAt,
		"world_config_size", len(world.WorldConfig),
		"world_state_size", len(world.WorldState))

	return world, nil
}

// getMapKeys 获取map的所有键（用于日志记录）
func getMapKeys(m map[string]interface{}) []string {
	if m == nil {
		return []string{}
	}
	keys := make([]string, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	return keys
}

// GetWorld 获取世界信息
func (s *WorldService) GetWorld(ctx context.Context, worldID uuid.UUID) (*models.World, error) {
	var world models.World
	err := s.db.WithContext(ctx).
		Preload("Creator").
		Preload("Scenes").
		Preload("Characters").
		First(&world, worldID).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("世界不存在")
		}
		s.logger.Error("获取世界信息失败", "error", err, "world_id", worldID)
		return nil, fmt.Errorf("获取世界信息失败: %w", err)
	}

	return &world, nil
}

// GetWorldsByCreator 获取用户创建的世界列表
func (s *WorldService) GetWorldsByCreator(ctx context.Context, creatorID uuid.UUID, limit, offset int) ([]models.World, int64, error) {
	var worlds []models.World
	var total int64

	query := s.db.WithContext(ctx).Where("creator_id = ?", creatorID)

	// 获取总数
	if err := query.Model(&models.World{}).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取世界总数失败: %w", err)
	}

	// 获取世界列表
	err := query.Preload("Creator").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&worlds).Error

	if err != nil {
		s.logger.Error("获取用户世界列表失败", "error", err, "creator_id", creatorID)
		return nil, 0, fmt.Errorf("获取世界列表失败: %w", err)
	}

	return worlds, total, nil
}

// GetPublicWorlds 获取公开世界列表
func (s *WorldService) GetPublicWorlds(ctx context.Context, limit, offset int) ([]models.World, int64, error) {
	var worlds []models.World
	var total int64

	query := s.db.WithContext(ctx).Where("is_public = ? AND status = ?", true, "active")

	// 获取总数
	if err := query.Model(&models.World{}).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取公开世界总数失败: %w", err)
	}

	// 获取世界列表
	err := query.Preload("Creator").
		Order("current_players DESC, created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&worlds).Error

	if err != nil {
		s.logger.Error("获取公开世界列表失败", "error", err)
		return nil, 0, fmt.Errorf("获取公开世界列表失败: %w", err)
	}

	return worlds, total, nil
}

// UpdateWorld 更新世界信息
func (s *WorldService) UpdateWorld(ctx context.Context, worldID uuid.UUID, updates map[string]interface{}) error {
	result := s.db.WithContext(ctx).Model(&models.World{}).Where("id = ?", worldID).Updates(updates)
	if result.Error != nil {
		s.logger.Error("更新世界信息失败", "error", result.Error, "world_id", worldID)
		return fmt.Errorf("更新世界信息失败: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("世界不存在")
	}

	s.logger.Info("成功更新世界信息", "world_id", worldID, "updates", updates)
	return nil
}

// DeleteWorld 删除世界
func (s *WorldService) DeleteWorld(ctx context.Context, worldID uuid.UUID, creatorID uuid.UUID) error {
	// 检查世界是否存在且属于该用户
	var world models.World
	err := s.db.WithContext(ctx).Where("id = ? AND creator_id = ?", worldID, creatorID).First(&world).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("世界不存在或无权限删除")
		}
		return fmt.Errorf("检查世界权限失败: %w", err)
	}

	// 软删除世界
	if err := s.db.WithContext(ctx).Delete(&world).Error; err != nil {
		s.logger.Error("删除世界失败", "error", err, "world_id", worldID, "creator_id", creatorID)
		return fmt.Errorf("删除世界失败: %w", err)
	}

	s.logger.Info("成功删除世界", "world_id", worldID, "creator_id", creatorID)
	return nil
}

// JoinWorld 加入世界
func (s *WorldService) JoinWorld(ctx context.Context, worldID uuid.UUID, userID uuid.UUID) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 获取世界信息
		var world models.World
		if err := tx.First(&world, worldID).Error; err != nil {
			return fmt.Errorf("世界不存在: %w", err)
		}

		// 检查世界是否可以加入
		if !world.CanJoin() {
			return fmt.Errorf("世界不可加入")
		}

		// 检查用户是否已经在世界中
		var existingChar models.Character
		err := tx.Where("world_id = ? AND user_id = ?", worldID, userID).First(&existingChar).Error
		if err == nil {
			return fmt.Errorf("用户已在该世界中")
		}
		if err != gorm.ErrRecordNotFound {
			return fmt.Errorf("检查用户状态失败: %w", err)
		}

		// 增加世界玩家数量
		if err := world.AddPlayer(tx); err != nil {
			return fmt.Errorf("增加玩家数量失败: %w", err)
		}

		s.logger.Info("用户成功加入世界", "world_id", worldID, "user_id", userID)
		return nil
	})
}

// LeaveWorld 离开世界
func (s *WorldService) LeaveWorld(ctx context.Context, worldID uuid.UUID, userID uuid.UUID) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 获取世界信息
		var world models.World
		if err := tx.First(&world, worldID).Error; err != nil {
			return fmt.Errorf("世界不存在: %w", err)
		}

		// 检查用户是否在世界中
		var character models.Character
		err := tx.Where("world_id = ? AND user_id = ?", worldID, userID).First(&character).Error
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return fmt.Errorf("用户不在该世界中")
			}
			return fmt.Errorf("检查用户状态失败: %w", err)
		}

		// 删除用户角色
		if err := tx.Delete(&character).Error; err != nil {
			return fmt.Errorf("删除角色失败: %w", err)
		}

		// 减少世界玩家数量
		if err := world.RemovePlayer(tx); err != nil {
			return fmt.Errorf("减少玩家数量失败: %w", err)
		}

		s.logger.Info("用户成功离开世界", "world_id", worldID, "user_id", userID)
		return nil
	})
}

// StartWorldTick 启动世界心跳
func (s *WorldService) StartWorldTick(ctx context.Context, worldID uuid.UUID) error {
	world, err := s.GetWorld(ctx, worldID)
	if err != nil {
		return err
	}

	if !world.IsActive() {
		return fmt.Errorf("世界未激活")
	}

	// 增加心跳数
	if err := world.IncrementTick(s.db); err != nil {
		s.logger.Error("增加世界心跳失败", "error", err, "world_id", worldID)
		return fmt.Errorf("增加世界心跳失败: %w", err)
	}

	// 更新游戏时间
	timeRate := world.GetTimeRate()
	tickInterval := world.GetTickInterval()
	gameTimeIncrement := int64(float64(tickInterval) * timeRate / 60) // 转换为分钟

	if err := world.UpdateGameTime(s.db, gameTimeIncrement); err != nil {
		s.logger.Error("更新游戏时间失败", "error", err, "world_id", worldID)
		return fmt.Errorf("更新游戏时间失败: %w", err)
	}

	s.logger.Debug("世界心跳更新", "world_id", worldID, "tick", world.GetCurrentTick(), "game_time", world.GameTime)
	return nil
}

// GetWorldStats 获取世界统计信息
func (s *WorldService) GetWorldStats(ctx context.Context, worldID uuid.UUID) (map[string]interface{}, error) {
	world, err := s.GetWorld(ctx, worldID)
	if err != nil {
		return nil, err
	}

	// 获取场景数量
	var sceneCount int64
	s.db.WithContext(ctx).Model(&models.Scene{}).Where("world_id = ?", worldID).Count(&sceneCount)

	// 获取角色数量
	var characterCount int64
	s.db.WithContext(ctx).Model(&models.Character{}).Where("world_id = ?", worldID).Count(&characterCount)

	// 获取实体数量
	var entityCount int64
	s.db.WithContext(ctx).Model(&models.Entity{}).Where("world_id = ?", worldID).Count(&entityCount)

	// 获取事件数量
	var eventCount int64
	s.db.WithContext(ctx).Model(&models.Event{}).Where("world_id = ?", worldID).Count(&eventCount)

	stats := map[string]interface{}{
		"world_id":        world.ID,
		"name":            world.Name,
		"status":          world.Status,
		"current_players": world.CurrentPlayers,
		"max_players":     world.MaxPlayers,
		"game_time":       world.GameTime,
		"current_tick":    world.GetCurrentTick(),
		"scene_count":     sceneCount,
		"character_count": characterCount,
		"entity_count":    entityCount,
		"event_count":     eventCount,
		"created_at":      world.CreatedAt,
		"updated_at":      world.UpdatedAt,
	}

	return stats, nil
}

// SetWorldPublic 设置世界公开状态
func (s *WorldService) SetWorldPublic(ctx context.Context, worldID uuid.UUID, creatorID uuid.UUID, isPublic bool) error {
	result := s.db.WithContext(ctx).Model(&models.World{}).
		Where("id = ? AND creator_id = ?", worldID, creatorID).
		Update("is_public", isPublic)

	if result.Error != nil {
		s.logger.Error("设置世界公开状态失败", "error", result.Error, "world_id", worldID)
		return fmt.Errorf("设置世界公开状态失败: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("世界不存在或无权限修改")
	}

	s.logger.Info("成功设置世界公开状态", "world_id", worldID, "is_public", isPublic)
	return nil
}

// UpdateWorldConfig 更新世界配置
func (s *WorldService) UpdateWorldConfig(ctx context.Context, worldID uuid.UUID, creatorID uuid.UUID, config map[string]interface{}) error {
	result := s.db.WithContext(ctx).Model(&models.World{}).
		Where("id = ? AND creator_id = ?", worldID, creatorID).
		Update("world_config", models.JSON(config))

	if result.Error != nil {
		s.logger.Error("更新世界配置失败", "error", result.Error, "world_id", worldID)
		return fmt.Errorf("更新世界配置失败: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("世界不存在或无权限修改")
	}

	s.logger.Info("成功更新世界配置", "world_id", worldID, "config", config)
	return nil
}

// GetActiveWorlds 获取活跃世界列表
func (s *WorldService) GetActiveWorlds(ctx context.Context) ([]models.World, error) {
	var worlds []models.World
	err := s.db.WithContext(ctx).
		Where("status = ? AND current_players > 0", "active").
		Preload("Creator").
		Order("current_players DESC").
		Find(&worlds).Error

	if err != nil {
		s.logger.Error("获取活跃世界列表失败", "error", err)
		return nil, fmt.Errorf("获取活跃世界列表失败: %w", err)
	}

	return worlds, nil
}
